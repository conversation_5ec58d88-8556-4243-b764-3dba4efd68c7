<template>
  <div class="vab-top-side-bar-container">
    <!-- 顶部导航 -->
    <div class="vab-top-bar">
      <el-menu
        :active-text-color="variables['menu-color-active']"
        :background-color="variables['menu-background']"
        :default-active="extra.first"
        mode="horizontal"
        :text-color="variables['menu-color']"
        @select="handleTopMenuSelect"
      >
        <template v-for="(route, index) in topRoutes">
          <el-menu-item
            v-if="route.meta && !route.meta.hidden"
            :key="index + route.name"
            :index="route.name"
          >
            <vab-icon
              v-if="route.meta.icon"
              :icon="route.meta.icon"
              :is-custom-svg="route.meta.isCustomSvg"
            />
            <span>{{ route.meta.title }}</span>
          </el-menu-item>
        </template>
      </el-menu>
    </div>

    <!-- 侧边栏导航 -->
    <el-scrollbar class="vab-side-bar" :class="{ 'is-collapse': collapse }">
      <vab-logo v-if="showLogo" />
      <el-menu
        :active-text-color="variables['menu-color-active']"
        :background-color="variables['menu-background']"
        :collapse="collapse"
        :collapse-transition="false"
        :default-active="activeMenu"
        :default-openeds="defaultOpeneds"
        menu-trigger="click"
        mode="vertical"
        :text-color="variables['menu-color']"
        :unique-opened="uniqueOpened"
      >
        <template v-for="(route, index) in sideRoutes">
          <vab-menu
            v-if="route.meta && !route.meta.hidden"
            :key="index + route.name"
            :item="route"
          />
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
  import variables from '@/vab/styles/variables/variables.scss'
  import { mapGetters } from 'vuex'
  import { defaultOpeneds, uniqueOpened } from '@/config'
  import { handleActivePath, handleMatched } from '@/utils/routes'

  export default {
    name: 'VabTopSideBar',
    props: {
      showLogo: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        activeMenu: '',
        defaultOpeneds,
        uniqueOpened,
        variables,
      }
    },
    computed: {
      ...mapGetters({
        extra: 'settings/extra',
        routes: 'routes/routes',
        collapse: 'settings/collapse',
        activeName: 'routes/activeName',
      }),
      // 顶部导航显示一级路由
      topRoutes() {
        return this.routes.filter(
          (route) => route.meta && route.meta.hidden !== true
        )
      },
      // 侧边栏显示当前选中的顶部导航的子路由
      sideRoutes() {
        const activeMenu = this.routes.find(
          (route) => route.name === this.extra.first
        )
        return activeMenu ? activeMenu.children : []
      },
    },
    watch: {
      $route: {
        handler(route) {
          this.activeMenu = handleActivePath(route)
          // 根据当前路由设置顶部菜单选中状态
          const matched = route.matched
          if (matched && matched.length > 0) {
            this.extra.first = matched[0].name
          }
        },
        immediate: true,
      },
      activeName: {
        handler(val) {
          const matched = handleMatched(this.routes, val)
          this.extra.first = matched[0].name
          this.activeMenu = matched[matched.length - 1].path
        },
      },
    },
    methods: {
      // 处理顶部菜单点击
      handleTopMenuSelect(index) {
        this.extra.first = index
        // 找到对应的路由
        const selectedRoute = this.routes.find((route) => route.name === index)
        // 如果有子路由，跳转到第一个子路由
        if (
          selectedRoute &&
          selectedRoute.children &&
          selectedRoute.children.length > 0
        ) {
          // 如果有重定向，优先使用重定向
          if (selectedRoute.redirect) {
            this.$router.push(selectedRoute.redirect)
          } else {
            // 否则跳转到第一个子路由
            const firstChild = selectedRoute.children[0]
            this.$router.push(firstChild.path)
          }
        } else if (selectedRoute) {
          // 如果没有子路由，直接跳转到当前路由
          this.$router.push(selectedRoute.path)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .vab-top-side-bar-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .vab-top-bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: $base-z-index + 2;
    width: 100%;
    height: $base-top-bar-height;
    overflow: hidden;
    background: $base-menu-background;
    box-shadow: $base-box-shadow;

    :deep() {
      .el-menu--horizontal {
        border-bottom: none;

        .el-menu-item {
          height: $base-top-bar-height;
          line-height: $base-top-bar-height;

          &:hover {
            color: $base-color-white;
            background-color: $base-menu-background-active !important;
          }

          &.is-active {
            color: $base-color-white;
            background-color: $base-menu-background-active !important;
          }
        }
      }
    }
  }

  .vab-side-bar {
    position: fixed;
    top: $base-top-bar-height;
    bottom: 0;
    left: 0;
    z-index: $base-z-index + 1;
    width: $base-left-menu-width;
    height: calc(100vh - #{$base-top-bar-height});
    overflow: hidden;
    background: $base-menu-background;
    box-shadow: $base-box-shadow;
    transition: $base-transition;

    &.is-collapse {
      width: $base-left-menu-width-min;
      border-right: 0;

      :deep() {
        .el-menu--collapse.el-menu {
          > .el-menu-item,
          > .el-submenu {
            text-align: center;

            .el-tag {
              display: none;
            }
          }
        }

        .el-menu-item,
        .el-submenu {
          text-align: left;
        }

        .el-menu--collapse {
          border-right: 0;

          .el-submenu__icon-arrow {
            right: 10px;
            margin-top: -3px;
          }
        }
      }
    }

    :deep() {
      .el-scrollbar__wrap {
        overflow-x: hidden;
      }

      .el-menu {
        border: 0;
      }

      .el-menu-item,
      .el-submenu__title {
        height: $base-menu-item-height;
        overflow: hidden;
        line-height: $base-menu-item-height;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;

        i {
          color: inherit;
        }
      }

      .el-menu-item {
        &:hover {
          color: $base-color-white;
          background-color: $base-menu-background-active !important;
        }

        &.is-active {
          color: $base-color-white;
          background-color: $base-menu-background-active !important;
        }
      }
    }
  }
</style>
