<template>
  <div class="app-wrapper" :class="classObj">
    <!-- 顶部侧边栏导航 -->
    <vab-top-side-bar />

    <!-- 主内容区域 -->
    <div class="main-container">
      <div class="fixed-header" :class="{ 'fixed-header-hidden': fullScreen }">
        <vab-tabs />
        <vab-nav />
      </div>
      <vab-app-main />
      <div class="footer-container">
        <vab-footer />
      </div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import VabTopSideBar from '@/vab/components/VabTopSideBar'
  import VabAppMain from '@/vab/components/VabAppMain'
  import VabFooter from '@/vab/components/VabFooter'
  import VabNav from '@/vab/components/VabNav'
  import VabTabs from '@/vab/components/VabTabs'

  export default {
    name: 'VabLayoutTopSide',
    components: {
      VabTopSideBar,
      Vab<PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>er,
      Vab<PERSON><PERSON>,
      VabTabs,
    },
    data() {
      return {}
    },
    computed: {
      ...mapGetters({
        device: 'settings/device',
        theme: 'settings/theme',
        collapse: 'settings/collapse',
        fullScreen: 'settings/fullScreen',
      }),
      classObj() {
        return {
          mobile: this.device === 'mobile',
          pc: this.device !== 'mobile',
          'is-collapse': this.collapse,
          'is-fixed': this.theme.fixedHeader,
          'is-fullscreen': this.fullScreen,
        }
      },
    },
    mounted() {},
  }
</script>

<style lang="scss" scoped>
  .app-wrapper {
    position: relative;
    width: 100%;
    height: 100%;

    &.is-fixed {
      padding-top: calc(#{$base-top-bar-height} + #{$base-tabs-height});
    }
    .main-container {
      position: relative;
      width: 100%;
      height: 100%;
      margin-left: $base-left-menu-width;
      background: $base-color-background;
      transition: margin-left $base-transition;

      ::v-deep {
        .fixed-header {
          position: fixed;
          top: $base-top-bar-height;
          right: 0;
          z-index: $base-z-index;
          width: calc(100% - #{$base-left-menu-width});
          overflow: hidden;
          transition: width $base-transition;

          &-hidden {
            height: 0 !important;
          }
        }
      }

      .footer-container {
        min-height: $base-footer-height;
      }
    }

    &.is-collapse {
      .main-container {
        margin-left: $base-left-menu-width-min;

        ::v-deep {
          .fixed-header {
            width: calc(100% - #{$base-left-menu-width-min});
          }
        }
      }
    }

    &.mobile {
      ::v-deep {
        .main-container {
          margin-left: 0;

          .fixed-header {
            width: 100%;
          }
        }
      }
    }
  }
</style>
