/**
 * @description 路由拦截状态管理，目前两种模式：all模式与intelligence模式，其中partialRoutes是菜单暂未使用
 */
import { getRouterList } from '@/api/user'
import { authentication, rolesControl } from '@/config'
import { asyncRoutes, constantRoutes, resetRouter } from '@/router'
import { convertRouter, filterRoutes } from '@/utils/routes'
import { isArray } from '@/utils/validate'
import Vue from 'vue'

const state = () => ({
  routes: [],
  activeName: '',
})
const getters = {
  routes: (state) => state.routes,
  activeName: (state) => state.activeName,
}
const mutations = {
  /**
   * @description 多模式设置路由
   * @param {*} state
   * @param {*} routes
   */
  setRoutes(state, routes) {
    state.routes = routes
  },
  /**
   * @description 修改Meta
   * @param {*} state
   * @param options
   */
  changeMenuMeta(state, options) {
    function handleRoutes(routes) {
      return routes.map((route) => {
        if (route.name === options.name) Object.assign(route.meta, options.meta)
        if (route.children && route.children.length)
          route.children = handleRoutes(route.children)
        return route
      })
    }
    state.routes = handleRoutes(state.routes)
  },
  /**
   * @description 修改 activeName
   * @param {*} state
   * @param activeName 当前激活菜单
   */
  changeActiveName(state, activeName) {
    state.activeName = activeName
  },
}
const actions = {
  /**
   * @description 多模式设置路由
   * @param {*} { commit }
   * @param mode
   * @returns
   */
  async setRoutes({ commit }, mode = 'none') {
    try {
      console.log('开始设置路由，模式:', mode)
      // 默认前端路由
      let asyncRoutesAll = asyncRoutes
      let routes = [...asyncRoutesAll]
      // 设置游客路由关闭路由拦截(不需要可以删除)
      const control = mode === 'visit' ? false : rolesControl

      // 设置后端路由(不需要可以删除)
      if (authentication === 'all') {
        console.log('使用后端路由模式，开始获取路由列表')
        try {
          const {
            data: { data, code },
          } = await getRouterList({
            clientId: 'hse-accident-event',
          })

          console.log('后端路由接口返回:', { code, data })

          if (code != '00000') {
            console.error('后端路由接口返回错误码:', code)
            Vue.prototype.$baseMessage(
              '获取路由权限失败，请联系管理员！',
              'error',
              'vab-hey-message-error'
            )
            // 如果后端路由获取失败，使用前端默认路由
            console.log('使用前端默认路由作为备选方案')
          } else if (!data || data.length == 0) {
            console.warn('后端返回的路由数据为空，使用前端默认路由')
            Vue.prototype.$baseMessage(
              '该账号没有授权，使用默认权限！',
              'warning',
              'vab-hey-message-warning'
            )
          } else {
            // 后端路由数据正常，进行转换
            var list = data
            if (!isArray(list)) {
              console.error('路由数据格式错误，不是数组:', list)
              Vue.prototype.$baseMessage(
                '路由格式返回有误！',
                'error',
                'vab-hey-message-error'
              )
            } else {
              console.log('开始转换后端路由数据:', list)
              if (list[list.length - 1].path !== '*')
                list.push({
                  path: '*',
                  redirect: '/404',
                  meta: { hidden: true },
                })
              routes = convertRouter(list)
              console.log('转换后的路由:', routes)
            }
          }
        } catch (error) {
          console.error('获取后端路由失败:', error)
          Vue.prototype.$baseMessage(
            '获取路由权限失败，使用默认权限！',
            'warning',
            'vab-hey-message-warning'
          )
          // 继续使用前端默认路由
        }
      }

      // 根据权限和rolesControl过滤路由
      const accessRoutes = filterRoutes([...constantRoutes, ...routes], control)
      console.log('过滤后的可访问路由:', accessRoutes)

      // 设置菜单所需路由
      commit('setRoutes', accessRoutes)

      // 根据可访问路由重置Vue Router
      console.log('开始重置路由器')
      await resetRouter(accessRoutes)
      console.log('路由设置完成')

      return accessRoutes
    } catch (error) {
      console.error('设置路由过程中发生错误:', error)
      Vue.prototype.$baseMessage(
        '路由初始化失败！',
        'error',
        'vab-hey-message-error'
      )
      throw error
    }
  },
  /**
   * @description 修改Route Meta
   * @param {*} { commit }
   * @param options
   */
  changeMenuMeta({ commit }, options = {}) {
    commit('changeMenuMeta', options)
  },
  /**
   * @description 修改 activeName
   * @param {*} { commit }
   * @param activeName 当前激活菜单
   */
  changeActiveName({ commit }, activeName) {
    commit('changeActiveName', activeName)
  },
}
export default { state, getters, mutations, actions }
