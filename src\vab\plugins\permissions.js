/**
 * @description 路由守卫，目前两种模式：all模式与intelligence模式
 */
import {
  authentication,
  loginInterception,
  openWaterMark,
  routesWhiteList,
  supportVisit,
} from '@/config'
import router from '@/router'
import store from '@/store'
import getPageTitle from '@/utils/pageTitle'
import { toLoginRoute } from '@/utils/routes'
import watermark from '@/utils/waterMarker.js'
import moment from 'moment'
import VabProgress from 'nprogress'
import 'nprogress/nprogress.css'
VabProgress.configure({
  easing: 'ease',
  speed: 500,
  trickleSpeed: 200,
  showSpinner: false,
})
router.beforeEach(async (to, _from, next) => {
  console.log('路由守卫：准备跳转到', to.path, '来自', _from.path)

  const { showProgressBar } = store.getters['settings/theme']
  if (showProgressBar) VabProgress.start()
  let hasToken = store.getters['user/token']

  if (!loginInterception) hasToken = true

  console.log(
    '路由守卫：hasToken =',
    !!hasToken,
    'loginInterception =',
    loginInterception
  )

  if (hasToken) {
    const routesLength = store.getters['routes/routes'].length
    console.log('路由守卫：当前路由数量 =', routesLength)

    if (routesLength) {
      isIframeUrl(to)
      // 禁止已登录用户返回登录页
      if (to.path === '/login') {
        console.log('路由守卫：已登录用户访问登录页，重定向到首页')
        next({ path: '/' })
        if (showProgressBar) VabProgress.done()
      } else if (to.matched.length > 0) {
        console.log('路由守卫：正常路由，允许访问')
        next()
        // let userInfo = store.getters['user/user']
        // if (userInfo.pwdChanged == 0) {
        //   Vue.prototype.$baseMessage(`请修改初始密码！`, 'error')
        //   next({ path: '/system/personalCenter' })
        // } else next()
      } else {
        console.log('路由守卫：路由未匹配，重定向到首页')
        // 如果路由没有匹配到，重定向到首页
        next({ path: '/', replace: true })
      }
      if (showProgressBar) VabProgress.done()
    } else {
      console.log('路由守卫：路由未初始化，开始初始化路由')
      try {
        if (loginInterception) {
          console.log('路由守卫：获取用户信息')
          await store.dispatch('user/getUserInfo')
        } else {
          console.log('路由守卫：设置虚拟角色')
          await store.dispatch('user/setVirtualRoles')
        }

        // 根据路由模式获取路由并根据权限过滤
        console.log('路由守卫：开始设置路由，authentication =', authentication)
        const routes = await store.dispatch('routes/setRoutes', authentication)
        console.log(
          '路由守卫：路由设置完成，路由数量 =',
          (routes && routes.length) || 0
        )

        console.log('路由守卫：重新跳转到目标路由')
        next({ ...to, replace: true })
      } catch (err) {
        console.error('路由守卫：初始化过程中发生错误:', err)
        await store.dispatch('user/resetAll')
        next(toLoginRoute(to.path))
      }
    }
  } else {
    console.log('路由守卫：用户未登录')
    if (routesWhiteList.includes(to.path)) {
      console.log('路由守卫：访问白名单路由')
      // 设置游客路由(不需要可以删除)
      if (supportVisit && !store.getters['routes/routes'].length) {
        console.log('路由守卫：设置游客路由')
        await store.dispatch('routes/setRoutes', 'visit')
        next({ ...to, replace: true })
      } else {
        console.log('路由守卫：直接允许访问白名单路由')
        next()
      }
    } else {
      console.log('路由守卫：非白名单路由，重定向到登录页')
      next(toLoginRoute(to.path))
    }
    if (showProgressBar) VabProgress.done()
  }
})
router.afterEach((to) => {
  document.title = getPageTitle(to.meta.title)
  if (VabProgress.status) VabProgress.done()
  //启用水印时，添加水印文字
  if (openWaterMark) {
    if (to.name !== 'Login') {
      const userInfo = store.getters['user/user']
      watermark.watermark({
        content: [
          userInfo.nickname || userInfo.username,
          moment().format('YYYY/MM/DD'),
          `HSE安全隐患`,
        ],
        // content: 'HSE安全隐患',//一行水印时，传字符串
      })
    } else {
      watermark.watermark({
        content: '', //如果当前是登录页，水印内容设置为空
      })
    }
  }
})

function isIframeUrl(params) {
  let routes = store.getters['routes/routes']
  var a
  let finddata = (route) => {
    route.forEach((i) => {
      if (i.name == params.name) {
        a = i
      } else {
        if (i.children && i.children.length > 0) {
          finddata(i.children)
        }
      }
    })
    return a
  }
  let d = finddata(routes)
  if (d) {
    //如果是外部链接，需要iframe内嵌的，将路由信息保存至store中
    let iframeInfo = d.isUrl ? d : {}
    store.dispatch('iframe/setIframeInfo', iframeInfo, { root: true })
  }
}
