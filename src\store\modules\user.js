/**
 * @description 登录、获取用户信息、退出登录、清除token逻辑，不建议修改
 */
import Vue from 'vue'
import { getUserInfo, login, socialLogin, getUser } from '@/api/user'
import { getToken, removeToken, setToken } from '@/utils/token'
import { resetRouter } from '@/router'
import { isArray, isString, isNumber } from '@/utils/validate'
import { title, tokenName } from '@/config'

// 缓存常量定义
const CACHE_KEYS = {
  USER_LIST: 'user_list_cache',
  PROJECT_ID: 'project_id_cache',
}

// 缓存操作工具
const cacheUtils = {
  // 获取缓存数据
  get: (key, defaultValue = null, isJson = false) => {
    const value = localStorage.getItem(key)
    if (value === null) return defaultValue
    return isJson ? JSON.parse(value) : value
  },

  // 设置缓存数据
  set: (key, value) => {
    if (value === undefined || value === null) {
      localStorage.removeItem(key)
      return
    }

    const valueToStore =
      typeof value === 'object' ? JSON.stringify(value) : value
    localStorage.setItem(key, valueToStore)
  },

  // 移除缓存数据
  remove: (key) => {
    localStorage.removeItem(key)
  },

  // 清除用户相关的所有缓存
  clearUserCache: () => {
    Object.values(CACHE_KEYS).forEach((key) => {
      localStorage.removeItem(key)
    })
  },
}

// 从本地缓存获取数据
const getCachedUserList = () => {
  return cacheUtils.get(CACHE_KEYS.USER_LIST, [], true)
}

const getCachedProjectId = () => {
  return cacheUtils.get(CACHE_KEYS.PROJECT_ID, '')
}

// 保存数据到本地缓存
const setCachedUserList = (userList) => {
  cacheUtils.set(CACHE_KEYS.USER_LIST, userList)
}

const setCachedProjectId = (projectId) => {
  cacheUtils.set(CACHE_KEYS.PROJECT_ID, projectId)
}

// 清除缓存
const clearUserCache = () => {
  cacheUtils.clearUserCache()
}

// 默认值常量
const DEFAULT_VALUES = {
  USERNAME: '游客',
  AVATAR: 'https://i.gtimg.cn/club/item/face/img/2/15922_100.gif',
}

// 状态初始化
const state = () => ({
  token: getToken(),
  username: DEFAULT_VALUES.USERNAME,
  user: {},
  userId: '',
  projectId: getCachedProjectId(), // 从缓存初始化
  avatar: DEFAULT_VALUES.AVATAR,
  userList: getCachedUserList(), // 从缓存初始化
})

// Getters - 简单直接返回状态
const getters = {
  token: (state) => state.token,
  username: (state) => state.username,
  avatar: (state) => state.avatar,
  user: (state) => state.user,
  userId: (state) => state.userId,
  projectId: (state) => state.projectId,
  userList: (state) => state.userList,
}

// Mutations - 状态修改
const mutations = {
  /**
   * @description 设置token
   * @param {Object} state Vuex状态
   * @param {String} token 用户令牌
   */
  setToken(state, token) {
    state.token = token
    setToken(token) // 同步到存储
  },

  /**
   * @description 设置用户名
   * @param {Object} state Vuex状态
   * @param {String} username 用户名
   */
  setUsername(state, username) {
    state.username = username
  },

  /**
   * @description 设置头像
   * @param {Object} state Vuex状态
   * @param {String} avatar 头像URL
   */
  setAvatar(state, avatar) {
    state.avatar = avatar
  },

  /**
   * @description 设置用户信息
   * @param {Object} state Vuex状态
   * @param {Object} user 用户信息对象
   */
  setUser(state, user) {
    state.user = user
  },

  /**
   * @description 设置用户id
   * @param {Object} state Vuex状态
   * @param {Number|String} userId 用户ID
   */
  setUserId(state, userId) {
    state.userId = userId
  },

  /**
   * @description 设置项目id
   * @param {Object} state Vuex状态
   * @param {String} projectId 项目ID
   */
  setProjectId(state, projectId) {
    state.projectId = projectId
    setCachedProjectId(projectId) // 更新缓存
  },

  /**
   * @description 设置用户列表
   * @param {Object} state Vuex状态
   * @param {Array} userList 用户列表数组
   */
  setUserList(state, userList) {
    state.userList = userList
    setCachedUserList(userList) // 更新缓存
  },
}
const actions = {
  /**
   * @description 登录拦截放行时，设置虚拟角色
   * @param {Object} context Vuex上下文对象
   */
  setVirtualRoles({ commit, dispatch }) {
    // 设置完全权限
    dispatch('acl/setFull', true, { root: true })

    // 设置默认用户信息
    commit('setAvatar', DEFAULT_VALUES.AVATAR)
    commit('setUsername', 'admin')
    commit('setUser', {})
    commit('setUserId', '')
  },
  /**
   * @description 登录
   * @param {Object} context Vuex上下文对象
   * @param {Object} userInfo 用户登录信息
   * @returns {Promise} 登录结果Promise
   */
  async login({ commit, dispatch }, userInfo) {
    try {
      console.log('用户登录：开始登录流程，参数:', userInfo)
      // 登录时清除缓存
      clearUserCache()

      console.log('用户登录：调用登录接口')
      const {
        data: { code, data, msg },
      } = await login(userInfo)

      console.log('用户登录：接口返回结果:', { code, data: !!data, msg })

      // 验证响应数据
      if (!data || code !== '00000') {
        const errorMsg = msg || `登录接口异常，未正确返回${tokenName}...`
        console.error('用户登录：登录失败，错误信息:', errorMsg)
        Vue.prototype.$baseMessage(errorMsg, 'error')
        return Promise.reject(new Error(errorMsg))
      }

      // 提取token信息
      const { access_token, token_type } = data
      console.log('用户登录：提取token信息:', {
        access_token: !!access_token,
        token_type,
      })

      if (access_token && token_type) {
        // 组合并设置token
        const accessToken = `${token_type} ${access_token}`
        console.log('用户登录：设置token到store')
        commit('setToken', accessToken)

        // 获取用户列表
        console.log('用户登录：获取用户列表')
        await dispatch('getUserList')

        console.log('用户登录：登录流程完成')
        return true
      } else {
        const errorMsg = msg || `登录接口异常，未正确返回${tokenName}...`
        console.error('用户登录：token信息不完整:', errorMsg)
        Vue.prototype.$baseMessage(errorMsg, 'error')
        return Promise.reject(new Error(errorMsg))
      }
    } catch (error) {
      console.error('用户登录：登录过程发生异常:', error)
      Vue.prototype.$baseMessage('登录过程发生错误', 'error')
      return Promise.reject(error)
    }
  },
  /**
   * @description 第三方登录
   * @param {Object} context Vuex上下文对象
   * @param {Object} tokenData 第三方登录token数据
   * @returns {Promise<void>} 登录结果Promise
   */
  async socialLogin({ commit }, tokenData) {
    try {
      const {
        data: { [tokenName]: token },
      } = await socialLogin(tokenData)

      if (!token) {
        const err = `login核心接口异常，请检查返回JSON格式是否正确，是否正确返回${tokenName}...`
        Vue.prototype.$baseMessage(err, 'error', 'vab-hey-message-error')
        throw new Error(err)
      }

      // 设置token
      commit('setToken', token)

      // 根据当前时间生成问候语
      const getGreeting = () => {
        const hour = new Date().getHours()

        if (hour < 8) return '早上好'
        if (hour <= 11) return '上午好'
        if (hour <= 13) return '中午好'
        if (hour < 18) return '下午好'
        return '晚上好'
      }

      // 显示欢迎通知
      Vue.prototype.$baseNotify(`欢迎登录${title}`, `${getGreeting()}！`)

      return token
    } catch (error) {
      console.error('第三方登录失败:', error)
      return Promise.reject(error)
    }
  },
  /**
   * @description 获取用户信息接口 这个接口非常非常重要，如果没有明确底层前逻辑禁止修改此方法，错误的修改可能造成整个框架无法正常使用
   * @param {Object} context Vuex上下文对象
   * @returns {Promise} 获取用户信息的Promise
   */
  async getUserInfo({ commit, dispatch }) {
    try {
      // 获取用户信息
      const {
        data: { data },
      } = await getUserInfo()

      if (!data) {
        throw new Error('获取用户信息失败，返回数据为空')
      }

      // 解构用户数据
      const { username, avatar, roles, permissions, userId, perms, clients } =
        data

      /**
       * 检验返回数据是否正常，无对应参数，将使用默认用户名,头像,Roles和Permissions
       * username {String}
       * avatar {String}
       * roles {List}
       * ability {List}
       */
      const isDataValid = !(
        (username && !isString(username)) ||
        (userId && !isNumber(userId)) ||
        (avatar && !isString(avatar)) ||
        (roles && !isArray(roles)) ||
        (permissions && !isArray(permissions)) ||
        (perms && !isArray(perms)) ||
        (clients && !isArray(clients))
      )

      if (!isDataValid) {
        const err = 'getUserInfo核心接口异常，请检查返回JSON格式是否正确'
        Vue.prototype.$baseMessage(err, 'error', 'vab-hey-message-error')
        throw new Error(err)
      }

      // 更新用户信息
      const userDataMap = [
        { value: username, action: 'setUsername' },
        { value: data, action: 'setUser' },
        { value: userId, action: 'setUserId' },
        { value: avatar, action: 'setAvatar' },
      ]

      // 设置用户基本信息
      userDataMap.forEach((item) => {
        if (item.value) commit(item.action, item.value)
      })

      // 设置权限相关信息
      const permissionsMap = [
        { value: roles, action: 'acl/setRole' },
        { value: permissions, action: 'acl/setPermission' },
        { value: perms, action: 'acl/setBtnRole' },
        { value: clients, action: 'acl/setClient' },
      ]

      // 设置权限信息
      permissionsMap.forEach((item) => {
        if (item.value) dispatch(item.action, item.value, { root: true })
      })

      return data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      Vue.prototype.$baseMessage(
        '获取用户信息失败',
        'error',
        'vab-hey-message-error'
      )
      return Promise.reject(error)
    }
  },
  /**
   * @description 退出登录
   * @param {Object} context Vuex上下文对象
   * @returns {Promise<void>} 退出登录结果Promise
   */
  async logout({ dispatch }) {
    try {
      // 注意：这里原本可能有调用后端登出接口的代码，现在被注释掉了
      // await logout()

      // 重置所有状态
      await dispatch('resetAll')
    } catch (error) {
      console.error('退出登录失败:', error)
      return Promise.reject(error)
    }
  },
  /**
   * @description 重置token、roles、permission、router、tabsBar等
   * @param {Object} context Vuex上下文对象
   * @returns {Promise<void>} 重置完成的Promise
   */
  async resetAll({ commit, dispatch }) {
    try {
      // 清除缓存
      clearUserCache()

      // 重置用户基本信息
      const userResetData = [
        { key: 'setUsername', value: DEFAULT_VALUES.USERNAME },
        { key: 'setUserId', value: '' },
        { key: 'setUser', value: {} },
        { key: 'setProjectId', value: '' },
        { key: 'setAvatar', value: DEFAULT_VALUES.AVATAR },
        { key: 'routes/setRoutes', value: [], root: true },
      ]

      // 应用重置
      userResetData.forEach((item) => {
        commit(item.key, item.value, item.root ? { root: true } : undefined)
      })

      // 重置权限相关信息
      const permissionResetActions = [
        { action: 'setToken', payload: '' },
        { action: 'acl/setFull', payload: false, root: true },
        { action: 'acl/setRole', payload: [], root: true },
        { action: 'acl/setPermission', payload: [], root: true },
        { action: 'tabs/delAllVisitedRoutes', payload: null, root: true },
      ]

      // 按顺序执行重置操作
      for (const item of permissionResetActions) {
        await dispatch(
          item.action,
          item.payload,
          item.root ? { root: true } : undefined
        )
      }

      // 重置路由
      await resetRouter()

      // 移除token
      removeToken()
    } catch (error) {
      console.error('重置用户信息失败:', error)
      return Promise.reject(error)
    }
  },
  /**
   * @description 设置token
   * @param {Object} context Vuex上下文对象
   * @param {String} token 用户令牌
   */
  setToken({ commit }, token) {
    commit('setToken', token)
  },

  /**
   * @description 设置头像
   * @param {Object} context Vuex上下文对象
   * @param {String} avatar 头像URL
   */
  setAvatar({ commit }, avatar) {
    commit('setAvatar', avatar)
  },

  /**
   * @description 设置项目id
   * @param {Object} context Vuex上下文对象
   * @param {String} projectId 项目ID
   */
  setProjectId({ commit }, projectId) {
    commit('setProjectId', projectId)
  },
  /**
   * @description 获取用户列表
   * @param {Object} context Vuex上下文对象
   * @returns {Promise<Array>} 用户列表Promise
   */
  async getUserList({ commit, state }) {
    try {
      console.log('获取用户列表：开始获取用户列表')

      // 如果已有数据，直接返回缓存数据
      if (state.userList && state.userList.length > 0) {
        console.log('获取用户列表：使用缓存数据，数量:', state.userList.length)
        return state.userList
      }

      console.log('获取用户列表：调用接口获取数据')
      // 否则请求接口获取数据
      const res = await getUser({
        pageNum: 1,
        pageSize: 9999,
      })

      console.log('获取用户列表：接口返回结果:', res)

      // 提取用户列表数据
      const userList =
        (res.data && res.data.data && res.data.data.list) ||
        (res.data && res.data.list) ||
        []
      console.log('获取用户列表：提取到的用户列表数量:', userList.length)

      // 更新状态和缓存
      commit('setUserList', userList)

      console.log('获取用户列表：完成')
      return userList
    } catch (error) {
      console.error('获取用户列表失败:', error)

      // 如果获取用户列表失败，设置空数组避免重复请求
      commit('setUserList', [])

      // 显示友好的错误提示
      Vue.prototype.$baseMessage(
        '获取用户列表失败，请检查网络连接',
        'warning',
        'vab-hey-message-warning'
      )

      // 不抛出错误，避免阻塞登录流程
      return []
    }
  },
}
export default { state, getters, mutations, actions }
